package vn.vinclub.partner.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.repository.PartnerPointTransactionHistoryRepository;
import vn.vinclub.partner.service.PartnerPointConfigService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TransactionLimitServiceImplTest {

    @Mock
    private PartnerPointTransactionHistoryRepository repository;

    @Mock
    private PartnerPointConfigService partnerPointConfigService;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RBucket<Long> countBucket;

    @Mock
    private RBucket<BigDecimal> amountBucket;

    @InjectMocks
    private TransactionLimitServiceImpl transactionLimitService;

    private static final Long PARTNER_ID = 1L;
    private static final String PARTNER_USER_ID = "user123";
    private static final String POINT_CODE = "VPOINT";
    private static final Long POINT_AMOUNT = 1000L;
    private static final String TODAY = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

    @BeforeEach
    void setUp() {
        // Set limits via reflection since @Value annotations won't work in unit tests
        try {
            var maxRequestField = TransactionLimitServiceImpl.class.getDeclaredField("maxRequestByUserPerDay");
            maxRequestField.setAccessible(true);
            maxRequestField.set(transactionLimitService, 10L);

            var maxCashAmountField = TransactionLimitServiceImpl.class.getDeclaredField("maxCashAmountByUserPerDay");
            maxCashAmountField.setAccessible(true);
            maxCashAmountField.set(transactionLimitService, 100000L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void validateDailyLimits_shouldPassWhenWithinLimits() {
        // Given
        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn(countBucket, amountBucket);
        when(countBucket.isExists()).thenReturn(true);
        when(countBucket.get()).thenReturn(5L); // Within limit of 10
        when(amountBucket.isExists()).thenReturn(true);
        when(amountBucket.get()).thenReturn(BigDecimal.valueOf(50000)); // Within limit of 100000

        // Mock cash value calculation
        when(partnerPointConfigService.findByPartnerIdAndCode(PARTNER_ID, POINT_CODE))
                .thenReturn(createMockPointConfig());

        // When & Then
        assertDoesNotThrow(() -> 
            transactionLimitService.validateDailyLimits(PARTNER_ID, PARTNER_USER_ID, POINT_AMOUNT, POINT_CODE)
        );
    }

    @Test
    void validateDailyLimits_shouldThrowExceptionWhenTransactionCountExceeded() {
        // Given
        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn(countBucket);
        when(countBucket.isExists()).thenReturn(true);
        when(countBucket.get()).thenReturn(10L); // At limit

        // When & Then
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () ->
            transactionLimitService.validateDailyLimits(PARTNER_ID, PARTNER_USER_ID, POINT_AMOUNT, POINT_CODE)
        );
        
        assertEquals(AppErrorCode.DAILY_TRANSACTION_COUNT_LIMIT_EXCEEDED, exception.getErrorCode());
    }

    @Test
    void validateDailyLimits_shouldThrowExceptionWhenCashAmountExceeded() {
        // Given
        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn(countBucket, amountBucket);
        when(countBucket.isExists()).thenReturn(true);
        when(countBucket.get()).thenReturn(5L); // Within count limit
        when(amountBucket.isExists()).thenReturn(true);
        when(amountBucket.get()).thenReturn(BigDecimal.valueOf(95000)); // Close to limit

        // Mock cash value calculation to return high value
        when(partnerPointConfigService.findByPartnerIdAndCode(PARTNER_ID, POINT_CODE))
                .thenReturn(createMockPointConfigHighValue());

        // When & Then
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () ->
            transactionLimitService.validateDailyLimits(PARTNER_ID, PARTNER_USER_ID, POINT_AMOUNT, POINT_CODE)
        );
        
        assertEquals(AppErrorCode.DAILY_CASH_AMOUNT_LIMIT_EXCEEDED, exception.getErrorCode());
    }

    @Test
    void getDailyTransactionCount_shouldReturnCachedValue() {
        // Given
        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn(countBucket);
        when(countBucket.isExists()).thenReturn(true);
        when(countBucket.get()).thenReturn(7L);

        // When
        Long result = transactionLimitService.getDailyTransactionCount(PARTNER_ID, PARTNER_USER_ID, TODAY);

        // Then
        assertEquals(7L, result);
        verify(repository, never()).countDailyTransactions(any(), any(), any(), any());
    }

    @Test
    void getDailyTransactionCount_shouldCalculateFromDatabaseWhenNotCached() {
        // Given
        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn(countBucket);
        when(countBucket.isExists()).thenReturn(false);
        when(repository.countDailyTransactions(eq(PARTNER_ID), eq(PARTNER_USER_ID), any(), any())).thenReturn(3L);

        // When
        Long result = transactionLimitService.getDailyTransactionCount(PARTNER_ID, PARTNER_USER_ID, TODAY);

        // Then
        assertEquals(3L, result);
        verify(repository).countDailyTransactions(eq(PARTNER_ID), eq(PARTNER_USER_ID), any(), any());
        verify(countBucket).set(eq(3L), any());
    }

    @Test
    void updateDailyCounters_shouldIncrementWhenTransactionCreatedWithCountedStatus() {
        // Given
        PartnerPointTransactionHistory newTransaction = createMockTransaction(PointHistoryStatus.PROCESSING);
        when(redissonClient.getBucket(anyString(), any(JsonJacksonCodec.class))).thenReturn(countBucket, amountBucket);
        when(countBucket.isExists()).thenReturn(true);
        when(countBucket.get()).thenReturn(5L);
        when(amountBucket.isExists()).thenReturn(true);
        when(amountBucket.get()).thenReturn(BigDecimal.valueOf(50000));

        // Mock cash value calculation
        when(partnerPointConfigService.findByPartnerIdAndCode(PARTNER_ID, POINT_CODE))
                .thenReturn(createMockPointConfig());

        // When
        transactionLimitService.updateDailyCounters(null, newTransaction);

        // Then
        verify(countBucket).set(eq(6L), any());
        verify(amountBucket).set(any(BigDecimal.class), any());
    }

    @Test
    void updateDailyCounters_shouldNotIncrementWhenTransactionCreatedWithNonCountedStatus() {
        // Given
        PartnerPointTransactionHistory newTransaction = createMockTransaction(PointHistoryStatus.FAILED);

        // When
        transactionLimitService.updateDailyCounters(null, newTransaction);

        // Then
        verify(redissonClient, never()).getBucket(anyString(), any(JsonJacksonCodec.class));
    }

    private PartnerPointTransactionHistory createMockTransaction(PointHistoryStatus status) {
        PartnerPointTransactionHistory transaction = new PartnerPointTransactionHistory();
        transaction.setPartnerId(PARTNER_ID);
        transaction.setPartnerUserId(PARTNER_USER_ID);
        transaction.setPointCode(POINT_CODE);
        transaction.setPointAmount(POINT_AMOUNT);
        transaction.setStatus(status);
        transaction.setRequestTime(System.currentTimeMillis());
        return transaction;
    }

    private Object createMockPointConfig() {
        // Mock point config that returns reasonable cash value
        var mockConfig = mock(Object.class);
        // This would need to be properly mocked based on actual PartnerPointConfig structure
        return mockConfig;
    }

    private Object createMockPointConfigHighValue() {
        // Mock point config that returns high cash value to trigger limit
        var mockConfig = mock(Object.class);
        return mockConfig;
    }
}
