package vn.vinclub.partner.repository;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface PartnerPointTransactionHistoryRepository extends JpaRepository<PartnerPointTransactionHistory, Long>, JpaSpecificationExecutor<PartnerPointTransactionHistory> {

    Optional<PartnerPointTransactionHistory> findByTransactionId(String transactionId);

    Optional<PartnerPointTransactionHistory> findByPartnerIdAndPartnerTransactionId(Long partnerId, String partnerTransactionId);

    Optional<PartnerPointTransactionHistory> findByInternalRefCode(String internalRefCode);

    /**
     * Find the sum of point amounts for successful transactions of a specific customer and type within a time range.
     *
     * @param partnerId       Partner ID
     * @param partnerUserId   Partner user ID
     * @param actorSystem     Actor system (PARTNER or VINCLUB)
     * @param transactionType Transaction type
     * @param statuses        Transaction statuses
     * @param startTime       Start time in milliseconds
     * @param endTime         End time in milliseconds
     * @return Sum of point amounts
     */
    @Query("SELECT COALESCE(SUM(p.pointAmount), 0) FROM PartnerPointTransactionHistory p " +
            "WHERE p.active = true " +
            "AND p.partnerId = :partnerId " +
            "AND p.partnerUserId = :partnerUserId " +
            "AND p.actorSystem = :actorSystem " +
            "AND p.transactionType = :transactionType " +
            "AND p.status in :statuses " +
            "AND p.requestTime >= :startTime " +
            "AND p.requestTime < :endTime")
    Long calculateUsedPointQuota(
            Long partnerId,
            String partnerUserId,
            ActorSystem actorSystem,
            TransactionType transactionType,
            Set<PointHistoryStatus> statuses,
            Long startTime,
            Long endTime);

    List<PartnerPointTransactionHistory> findAllByStatus(PointHistoryStatus pointHistoryStatus, Pageable pageable);

    /**
     * Count transactions for a specific user on a specific day with PROCESS or SUCCESS status
     *
     * @param partnerId     Partner ID
     * @param partnerUserId Partner user ID
     * @param startTime     Start time in milliseconds (start of day)
     * @param endTime       End time in milliseconds (end of day)
     * @return Count of transactions
     */
    @Query("SELECT COUNT(p) FROM PartnerPointTransactionHistory p " +
            "WHERE p.active = true " +
            "AND p.partnerId = :partnerId " +
            "AND p.partnerUserId = :partnerUserId " +
            "AND p.status IN ('PROCESSING', 'SUCCESS') " +
            "AND p.requestTime >= :startTime " +
            "AND p.requestTime < :endTime")
    Long countDailyTransactions(
            Long partnerId,
            String partnerUserId,
            Long startTime,
            Long endTime);

    /**
     * Calculate total cash amount for transactions of a specific user on a specific day with PROCESS or SUCCESS status
     * This query joins with partner_point_config to get exchange rates
     *
     * @param partnerId     Partner ID
     * @param partnerUserId Partner user ID
     * @param startTime     Start time in milliseconds (start of day)
     * @param endTime       End time in milliseconds (end of day)
     * @return Total cash amount in VND
     */
    @Query("SELECT COALESCE(SUM(p.pointAmount * " +
            "CASE WHEN ppc.exchangeCashRate.exchangeValue IS NOT NULL AND ppc.exchangeCashRate.pointValue IS NOT NULL " +
            "THEN ppc.exchangeCashRate.exchangeValue / ppc.exchangeCashRate.pointValue " +
            "ELSE 0 END), 0) " +
            "FROM PartnerPointTransactionHistory p " +
            "LEFT JOIN PartnerPointConfig ppc ON p.partnerId = ppc.partnerId AND p.pointCode = ppc.code " +
            "WHERE p.active = true " +
            "AND p.partnerId = :partnerId " +
            "AND p.partnerUserId = :partnerUserId " +
            "AND p.status IN ('PROCESSING', 'SUCCESS') " +
            "AND p.requestTime >= :startTime " +
            "AND p.requestTime < :endTime")
    BigDecimal calculateDailyCashAmount(
            Long partnerId,
            String partnerUserId,
            Long startTime,
            Long endTime);
}
