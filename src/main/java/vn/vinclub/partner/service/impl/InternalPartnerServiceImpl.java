package vn.vinclub.partner.service.impl;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.constant.AppConst;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.constant.MetadataKey;
import vn.vinclub.partner.domain.dto.external.point.PartnerPointTxnResponse;
import vn.vinclub.partner.domain.dto.module.config.TopUpPointConfig;
import vn.vinclub.partner.domain.dto.partner.PartnerFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointConfigFilterDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.dto.request.TopUpPartnerPointIntRequest;
import vn.vinclub.partner.domain.dto.response.LinkedAccountIntResponse;
import vn.vinclub.partner.domain.dto.response.PartnerIntResponse;
import vn.vinclub.partner.domain.dto.response.PartnerPointConfigIntResponse;
import vn.vinclub.partner.domain.dto.response.PartnerPointTxnHistoryIntResponse;
import vn.vinclub.partner.domain.entity.Partner;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.entity.PartnerUserMapping;
import vn.vinclub.partner.domain.enums.*;
import vn.vinclub.partner.domain.event.historical.CustomerHistoricalEventData;
import vn.vinclub.partner.domain.event.historical.HistoricalEvent;
import vn.vinclub.partner.domain.event.internal.PartnerPointTxnRequestEvent;
import vn.vinclub.partner.domain.mapper.PartnerMapper;
import vn.vinclub.partner.domain.mapper.PartnerPointConfigMapper;
import vn.vinclub.partner.domain.mapper.PartnerPointTransactionHistoryMapper;
import vn.vinclub.partner.domain.mapper.PartnerUserMappingMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.exception.IgnoreProcessingException;
import vn.vinclub.partner.module.account.registry.PartnerLinkAccountServiceRegistry;
import vn.vinclub.partner.module.point.registry.PartnerPointServiceRegistry;
import vn.vinclub.partner.redis.VClubLock;
import vn.vinclub.partner.service.*;

import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class InternalPartnerServiceImpl implements InternalPartnerService {
    private final PartnerService partnerService;
    private final PartnerModuleService partnerModuleService;
    private final PartnerUserMappingService partnerUserMappingService;
    private final PartnerPointTransactionHistoryService partnerPointTransactionHistoryService;

    private final IntegrationPointService integrationPointService;
    private final EventService eventService;

    private final PartnerLinkAccountServiceRegistry partnerLinkAccountServiceRegistry;
    private final PartnerPointServiceRegistry partnerPointServiceRegistry;

    private final PlatformTransactionManager transactionManager;
    private final RedissonClient redissonClient;
    private final BaseJsonUtils jsonUtils;
    private final PartnerPointConfigService partnerPointConfigService;
    private final TransactionLimitService transactionLimitService;

    @Profiler
    @Override
    public void handleVClubCustomerHistoricalEvent(HistoricalEvent<CustomerHistoricalEventData> event) {
        Optional<Long> customerId = checkDeleteCustomerFromHistorical(event);
        if (customerId.isEmpty()) {
            return;
        }
        // get all partner linked account then call unlink account
        var partnerUserMappings = partnerUserMappingService.findByVclubUserIdAndActiveTrue(customerId.get());

        if (CollectionUtils.isEmpty(partnerUserMappings)) {
            return;
        }

        var deleteAt = System.currentTimeMillis();
        partnerUserMappings.forEach(mapping -> {
            var partner = partnerService.optById(mapping.getPartnerId());

            // call unlink account api to partner
            partner.ifPresent(p -> {
                partnerLinkAccountServiceRegistry.getService(p.getCode()).unlinkAccount(mapping.getVclubUserId(), deleteAt);
            });

        });
    }

    @Profiler
    @Override
    public void handlePartnerUserMappingHistoricalEvent(HistoricalEvent<PartnerUserMapping> event) {
        if (event == null || event.getData() == null || event.getOldData() == null) {
            return;
        }

        PartnerUserMapping data = event.getData();
        PartnerUserMapping oldData = event.getOldData();

        var id = Optional.ofNullable(data.getId()).orElse(oldData.getId());

        var partnerId = Optional.ofNullable(data.getPartnerId()).orElse(oldData.getPartnerId());
        var partnerUserId = Optional.ofNullable(data.getPartnerUserId()).orElse(oldData.getPartnerUserId());
        var vclubUserId = Optional.ofNullable(data.getVclubUserId()).orElse(oldData.getVclubUserId());

        partnerUserMappingService.invalidateCache(id);
        partnerUserMappingService.invalidateCache(partnerId, partnerUserId);
        partnerUserMappingService.invalidateCache(partnerId, vclubUserId);
    }

    @Profiler
    @Override
    public void handlePartnerPointTransactionHistoricalEvent(HistoricalEvent<PartnerPointTransactionHistory> event) {
        if (event == null || event.getData() == null || event.getOldData() == null) {
            return;
        }

        PartnerPointTransactionHistory data = event.getData();
        PartnerPointTransactionHistory oldData = event.getOldData();

        var transactionId = Optional.ofNullable(data.getTransactionId()).orElse(oldData.getTransactionId());
        partnerPointTransactionHistoryService.invalidateCache(transactionId);

        var partnerId = Optional.ofNullable(data.getPartnerId()).orElse(oldData.getPartnerId());
        var partnerUserId = Optional.ofNullable(data.getPartnerUserId()).orElse(oldData.getPartnerUserId());
        var transactionType = Optional.ofNullable(data.getTransactionType()).orElse(oldData.getTransactionType());

        integrationPointService.invalidateUsedPointQuotaCache(partnerId, partnerUserId, transactionType);
        invalidateUsedPointQuotaCache(partnerId, partnerUserId, transactionType);

        // Update transaction limit counters
        transactionLimitService.updateDailyCounters(oldData, data);

    }

    @Profiler
    @Override
    public PartnerPointTxnHistoryIntResponse topUpPoint(TopUpPartnerPointIntRequest request) {
        log.debug("topUpPoint: {}", jsonUtils.toString(request));
        var partner = partnerService.findById(request.getPartnerId());
        var mapping = partnerUserMappingService.optByPartnerIdAndVClubUserId(partner.getId(), request.getVclubUserId());

        if (mapping.isEmpty()) {
            throw new BusinessLogicException(AppErrorCode.ACCOUNT_NOT_LINKED);
        }
        request.setPartnerUserId(mapping.get().getPartnerUserId());

        validateTopUpPointRequest(request);

        // create metadata
        ObjectNode metadata = null;
        if (request.getMetadata() != null) {
            metadata = JsonNodeFactory.instance.objectNode();
            metadata.set(MetadataKey.PartnerTransaction.TOPUP_EXTRA_INFO, request.getMetadata());
        }

        // Init transaction
        var outboxId = UUID.randomUUID().toString();
        TransactionStatus tx = null;
        PartnerPointTransactionHistory transaction;

        // persist transaction history with outbox event
        try (var p1 = new vn.vinclub.common.model.Profiler(getClass(), "topUpPoint - transaction")) {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            tx = transactionManager.getTransaction(def);
            transaction = partnerPointTransactionHistoryService.initTopUpPointTxnToPartner(
                    partner.getId(),
                    mapping.get().getPartnerUserId(),
                    request.getVclubUserId(),
                    request.getTopUpPoint(),
                    request.getPointTypeCode(),
                    request.getDescription(),
                    metadata,
                    request.getInternalRefCode()
            );

            eventService.persistOutboxEvent(
                    PartnerPointTransactionHistoryMapper.INSTANCE.toRequestEvent(transaction),
                    outboxId
            );

            transactionManager.commit(tx);
        } catch (Exception e) {
            log.error("Error while persist transaction history: {}", e.getMessage());
            throw e;
        } finally {
            if (Objects.nonNull(tx) && !tx.isCompleted()) {
                transactionManager.rollback(tx);
            }
        }
        // send outbox event
        try (var p2 = new vn.vinclub.common.model.Profiler(getClass(), "topUpPoint - sendOutboxEvent")) {
            eventService.sendOutboxEvent(outboxId, null);
        } catch (Exception e) {
            log.error("Error while sending outbox event: {}", outboxId, e);
        }

        return PartnerPointTransactionHistoryMapper.INSTANCE.toInternalResponseDto(transaction);
    }

    @Profiler
    @Override
    public void handlePartnerPointTxnRequestEvent(PartnerPointTxnRequestEvent event) {
        var transaction = partnerPointTransactionHistoryService.optByTransactionId(event.getTransactionId())
                .orElseThrow(() -> new IgnoreProcessingException(AppErrorCode.TRANSACTION_NOT_FOUND));

        try (var customerLock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_USER_MAPPING_CUD + event.getCustomerLockKey())) {
            var partner = partnerService.findById(event.getPartnerId());
            var isMapped = partnerUserMappingService.isMapped(partner.getId(), event.getPartnerUserId(), event.getVclubUserId());

            if (!isMapped) {
                throw new BusinessLogicException(AppErrorCode.ACCOUNT_NOT_LINKED);
            }
            try (var txnLock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_POINT_TRANSACTION_CUD + event.getTransactionId())) {
                transaction = partnerPointTransactionHistoryService.optByTransactionId(event.getTransactionId())
                        .orElseThrow(() -> new IgnoreProcessingException(AppErrorCode.TRANSACTION_NOT_FOUND));

                // check data event
                if (!transaction.getInternalRefCode().equals(event.getInternalRefCode())
                        || !transaction.getActorSystem().equals(event.getActorSystem())
                        || !transaction.getTransactionType().equals(event.getTransactionType())
                        || !transaction.getVclubUserId().equals(event.getVclubUserId())
                        || !transaction.getPartnerUserId().equals(event.getPartnerUserId())
                        || !transaction.getPointAmount().equals(event.getPointAmount())
                        || !transaction.getPartnerId().equals(event.getPartnerId())) {
                    throw new IgnoreProcessingException(AppErrorCode.EVENT_DATA_MISMATCH);
                }

                // check transaction status
                if (!PointHistoryStatus.PROCESSING.equals(transaction.getStatus())) {
                    throw new IgnoreProcessingException(AppErrorCode.INVALID_TRANSACTION_STATUS);
                }

                // check transaction actor system
                if (ActorSystem.PARTNER.equals(transaction.getActorSystem())) {
                    throw new IgnoreProcessingException(AppErrorCode.INVALID_TRANSACTION_ACTOR_SYSTEM);
                }

                // process call partner api to process transaction
                PartnerPointTxnResponse response = switch (event.getTransactionType()) {
                    case TOP_UP_POINT ->
                            partnerPointServiceRegistry.getService(partner.getCode()).topUpPoint(transaction);
                    case SPEND_POINT ->
                            partnerPointServiceRegistry.getService(partner.getCode()).spendPoint(transaction);
                    default -> throw new IgnoreProcessingException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION);
                };

                if (PointHistoryStatus.SUCCESS.equals(response.getStatus())) {
                    processTopUpPointSuccess(response);
                } else if (PointHistoryStatus.FAILED.equals(response.getStatus())) {
                    processTopUpPointFailed(transaction.getTransactionId(), response.getFailedReason(), response.getRequest(), response.getResponse());
                } else if (PointHistoryStatus.PROCESSING.equals(response.getStatus())) {
                    processTopUpPointProcessing(transaction.getTransactionId(), response.getRequest());
                } else {
                    throw new IgnoreProcessingException(AppErrorCode.INVALID_TRANSACTION_STATUS);
                }
            }
        } catch (IgnoreProcessingException e) {
            // throw exception to ignore processing
            throw e;
        } catch (Exception e) {
            if ((e instanceof BusinessLogicException ex)) {
                processTopUpPointFailed(transaction.getTransactionId(), ex.getPayload().getFirstMessage(), null, null);
                log.error("Handle partner point txn request event error: {}", ex.getMessage());
            } else {
                processTopUpPointFailed(transaction.getTransactionId(), e.getMessage(), null, null);
                log.error("Handle partner point txn request event error: {}", e.getMessage(), e);
            }
        }
    }

    @Profiler
    @Override
    public Page<PartnerIntResponse> filterPartners(PartnerFilterDto filter, Pageable pageable) {
        var partners = partnerService.filter(filter, pageable);
        return partners.map(this::buildInternalPartnerResponse);
    }

    @Profiler
    @Override
    public PartnerIntResponse getPartnerById(String partnerId) {
        var partner = partnerService.findById(Long.parseLong(partnerId));
        return buildInternalPartnerResponse(partner);
    }

    @Override
    @Profiler
    public Map<Long, PartnerIntResponse> multiGetPartners(List<Long> partnerIds, Boolean includeDisabled) {
        if (CollectionUtils.isEmpty(partnerIds)) {
            return Map.of();
        }

        var partners = new HashMap<Long, PartnerIntResponse>();

        partnerIds.forEach(partnerId -> {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "multiGetPartners.item")) {
                Partner partner;
                if (includeDisabled) {
                    partner = partnerService.optById(partnerId).orElse(null);
                } else {
                    partner = partnerService.findById(partnerId);
                }
                if (Objects.nonNull(partner)) {
                    partners.put(partner.getId(), buildInternalPartnerResponse(partner));
                }
            } catch (Exception e) {
                log.warn("Cannot get partner with id={}, error={}", partnerId, e.getMessage());
            }
        });

        return partners;
    }

    @Profiler
    @Override
    public Map<String, PartnerIntResponse> multiGetPartnersByCode(List<String> partnerCodes, Boolean includeDisabled) {
        if (CollectionUtils.isEmpty(partnerCodes)) {
            return Map.of();
        }

        var partners = new HashMap<String, PartnerIntResponse>();

        partnerCodes.forEach(partnerCode -> {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "multiGetPartnersByCode.item")) {
                Partner partner;
                if (includeDisabled) {
                    partner = partnerService.optByCode(partnerCode).orElse(null);
                } else {
                    partner = partnerService.findByCode(partnerCode);
                }
                if (Objects.nonNull(partner)) {
                    partners.put(partner.getCode(), buildInternalPartnerResponse(partner));
                }
            } catch (Exception e) {
                log.warn("Cannot get partner with code={}, error={}", partnerCode, e.getMessage());
            }
        });

        return partners;
    }

    @Profiler
    @Override
    public void recheckProcessingTransaction() {
        while (true) {
            var pageable = PageRequest.ofSize(1000);
            var transactions = partnerPointTransactionHistoryService.findProcessingTransactions(pageable);
            if (CollectionUtils.isEmpty(transactions)) {
                break;
            }
            transactions.stream()
                    .filter(PartnerPointTransactionHistory::getActive)
                    .forEach(transaction -> {
                        if (ActorSystem.PARTNER.equals(transaction.getActorSystem())) {
                            integrationPointService.recheckProcessingTransactionFromPartner(transaction);
                        } else {
                            recheckProcessingTransactionFromVinclub(transaction);
                        }
                    });
            if (transactions.size() < pageable.getPageSize()) {
                break;
            }
        }
    }

    @Profiler
    @Override
    public LinkedAccountIntResponse getLinkedAccount(Long partnerId, Long vclubUserId) {
        var partner = partnerService.findById(partnerId);
        var mapping = partnerUserMappingService.optByPartnerIdAndVClubUserId(partner.getId(), vclubUserId);

        if (mapping.isEmpty()) {
            return LinkedAccountIntResponse.builder()
                    .isLinked(false)
                    .build();
        }

        var linkedAccount = PartnerUserMappingMapper.INSTANCE.toLinkedAccountIntResponse(mapping.get());
        linkedAccount.setPartnerCode(partner.getCode());

        return linkedAccount;
    }

    @Profiler
    @Override
    public Page<PartnerPointTxnHistoryIntResponse> filterPartnerPointTransactionHistory(PartnerPointTxnFilterDto filter, Pageable pageable) {
        var histories = partnerPointTransactionHistoryService.filter(filter, pageable);
        return histories.map(PartnerPointTransactionHistoryMapper.INSTANCE::toInternalResponseDto);
    }

    @Override
    @Profiler
    public PartnerPointTxnHistoryIntResponse getPartnerPointTransactionHistory(String transactionId) {
        var transaction = partnerPointTransactionHistoryService.findByTransactionId(transactionId);
        return PartnerPointTransactionHistoryMapper.INSTANCE.toInternalResponseDto(transaction);
    }

    @Profiler
    private PartnerIntResponse buildInternalPartnerResponse(Partner partner) {
        var response = PartnerMapper.INSTANCE.toIntResponse(partner);

        // enhance metadata
        if (Objects.nonNull(partner.getMetadata())) {
            var interactionLink = partner.getMetadata().get(MetadataKey.Partner.INTERACTION_LINKS);
            if (Objects.nonNull(interactionLink)) {
                response.setInteractionLinks(jsonUtils.readMapFromJson(interactionLink));
            }
        }

        // get point configs
        var pointConfigs = partnerPointConfigService.filter(PartnerPointConfigFilterDto.builder()
                .partnerId(partner.getId())
                .active(true)
                .build(), Pageable.unpaged());

        response.setPointConfigs(pointConfigs.stream()
                .map(PartnerPointConfigMapper.INSTANCE::toIntResponseDto)
                .collect(Collectors.toMap(PartnerPointConfigIntResponse::getPointTypeCode, Function.identity()))
        );

        return response;
    }

    @Profiler
    private void recheckProcessingTransactionFromVinclub(PartnerPointTransactionHistory transaction) {
        try (var txnLock = new VClubLock(redissonClient, AppConst.REDIS_LOCK.PARTNER_POINT_TRANSACTION_CUD + transaction.getTransactionId())) {
            var partner = partnerService.findById(transaction.getPartnerId());
            var response = partnerPointServiceRegistry.getService(partner.getCode()).checkTimeoutTxn(transaction);

            if (PointHistoryStatus.SUCCESS.equals(response.getStatus())) {
                processTopUpPointSuccess(response);
            } else if (PointHistoryStatus.FAILED.equals(response.getStatus())) {
                processTopUpPointFailed(transaction.getTransactionId(), response.getFailedReason(), response.getRequest(), response.getResponse());
            } else if (PointHistoryStatus.PROCESSING.equals(response.getStatus())) {
                log.info("Transaction is still processing: {}", transaction.getTransactionId());
            }

        } catch (Exception e) {
            log.error("Exception occurs when recheck processing transaction: {}", e.getMessage(), e);
        }
    }

    @Profiler
    private void validateTopUpPointRequest(TopUpPartnerPointIntRequest request) {
        // Check enabled module
        var module = partnerModuleService.optByPartnerAndModule(request.getPartnerId(), ModuleType.TOPUP_POINT);
        if (module.isEmpty() || !module.get().isEnabled()) {
            throw new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION);
        }

        // check enabled vinclub flow
        var config = jsonUtils.treeToValue(module.get().getConfig(), TopUpPointConfig.class);
        if (!config.isPartnerEnabled()) {
            throw new BusinessLogicException(AppErrorCode.PARTNER_UNSUPPORTED_OPERATION);
        }

        // Check if request not define point code then get default point code
        if (!StringUtils.hasText(request.getPointTypeCode())) {
            var defaultPointConfig = partnerPointConfigService.filter(PartnerPointConfigFilterDto.builder()
                    .partnerId(request.getPartnerId())
                    .isDefault(true)
                    .build(), PageRequest.ofSize(1));

            if (!defaultPointConfig.isEmpty() && defaultPointConfig.hasContent()) {
                request.setPointTypeCode(defaultPointConfig.getContent().getFirst().getCode());
            }
        }

        // validate point code
        if (!StringUtils.hasText(request.getPointTypeCode())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, "pointTypeCode");
        }

        if (!config.getAllowPartnerPointCodes().contains(request.getPointTypeCode())) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "pointTypeCode", "pointTypeCode is not allow");
        }

        var pointConfig = partnerPointConfigService.findByCode(request.getPartnerId(),request.getPointTypeCode());
        if (!PartnerStatus.ACTIVE.equals(pointConfig.getStatus())) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "pointTypeCode", "pointTypeCode is not active");
        }

        if (request.getTopUpPoint() <= 0) {
            throw new BusinessLogicException(AppErrorCode.INVALID_PARAM, "topUpPoint", "topUpPoint phải lớn hơn 0");
        }

        // check limit
        if (config.isPartnerLimitEnabled() && request.getTopUpPoint() > config.getMaxPartnerPointPerTransaction()) {
            var errorData = Map.of(
                    "maxPointPerTransaction", config.getMaxPartnerPointPerTransaction()
            );
            throw new BusinessLogicException(errorData, AppErrorCode.POINT_PER_TRANSACTION_LIMIT_EXCEEDED);
        }

        // check daily quota
        if (config.isPartnerDailyQuotaEnabled()) {
            Long remainingDailyQuota = calculateRemainingTopUpPointDailyQuota(request.getPartnerId(), request.getPartnerUserId(), config.getPartnerDailyQuota());
            if (request.getTopUpPoint() > remainingDailyQuota) {
                var errorData = Map.of(
                        "dailyQuota", config.getPartnerDailyQuota(),
                        "remainingDailyQuota", remainingDailyQuota
                );
                throw new BusinessLogicException(errorData, AppErrorCode.DAILY_POINT_QUOTA_EXCEEDED);
            }
        }

        // check monthly quota
        if (config.isPartnerMonthlyQuotaEnabled()) {
            Long remainingMonthlyQuota = calculateRemainingTopUpPointMonthlyQuota(request.getPartnerId(), request.getPartnerUserId(), config.getPartnerMonthlyQuota());
            if (request.getTopUpPoint() > remainingMonthlyQuota) {
                var errorData = Map.of(
                        "monthlyQuota", config.getPartnerMonthlyQuota(),
                        "remainingMonthlyQuota", remainingMonthlyQuota
                );
                throw new BusinessLogicException(errorData, AppErrorCode.MONTHLY_POINT_QUOTA_EXCEEDED);
            }
        }

    }

    @Profiler
    private Long calculateRemainingTopUpPointMonthlyQuota(Long partnerId, String partnerUserId, Long partnerMonthlyQuota) {
        String key = String.format("partner_svc_topup_partner_point_monthly_quota_used:%s:%s", partnerId, partnerUserId);
        RBucket<Long> bucket = redissonClient.getBucket(key, new LongCodec());
        if (bucket.isExists()) {
            return Math.max(0, partnerMonthlyQuota - bucket.get());
        }

        var recalculated = partnerPointTransactionHistoryService.getPartnerUserMonthlyQuotaUsed(ActorSystem.VINCLUB, partnerId, partnerUserId, TransactionType.TOP_UP_POINT);
        if (recalculated != null) {
            bucket.set(recalculated, Duration.ofMinutes(15));
            return Math.max(0, partnerMonthlyQuota - recalculated);
        }

        return partnerMonthlyQuota;
    }

    @Profiler
    private Long calculateRemainingTopUpPointDailyQuota(Long partnerId, String partnerUserId, Long partnerDaiLyQuota) {
        String key = String.format("partner_svc_topup_partner_point_daily_quota_used:%s:%s", partnerId, partnerUserId);
        RBucket<Long> bucket = redissonClient.getBucket(key, new LongCodec());
        if (bucket.isExists()) {
            return Math.max(0, partnerDaiLyQuota - bucket.get());
        }

        var recalculated = partnerPointTransactionHistoryService.getPartnerUserDailyQuotaUsed(ActorSystem.VINCLUB, partnerId, partnerUserId, TransactionType.TOP_UP_POINT);
        if (recalculated != null) {
            bucket.set(recalculated, Duration.ofMinutes(15));
            return Math.max(0, partnerDaiLyQuota - recalculated);
        }

        return partnerDaiLyQuota;
    }

    @Profiler
    private void invalidateUsedPointQuotaCache(Long partnerId, String partnerUserId, TransactionType transactionType) {
        if (transactionType == TransactionType.TOP_UP_POINT) {
            String key = String.format("partner_svc_topup_partner_point_daily_quota_used:%s:%s", partnerId, partnerUserId);
            redissonClient.getBucket(key).delete();
            key = String.format("partner_svc_topup_partner_point_monthly_quota_used:%s:%s", partnerId, partnerUserId);
            redissonClient.getBucket(key).delete();
        }
    }

    @Profiler
    private void processTopUpPointProcessing(String transactionId, String request) {
            partnerPointTransactionHistoryService.logRequest(transactionId, jsonUtils.toNode(request));
    }

    @Profiler
    private void processTopUpPointFailed(String transactionId, String failedReason, String request, String response) {

        // Init transaction
        var outboxId = UUID.randomUUID().toString();
        TransactionStatus tx = null;
        try (var p1 = new vn.vinclub.common.model.Profiler(getClass(), "processTopUpPointFailed - transaction")) {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            tx = transactionManager.getTransaction(def);
            var txn = partnerPointTransactionHistoryService.markTxnFailed(transactionId, failedReason, request, response);
            eventService.persistOutboxEvent(
                    PartnerPointTransactionHistoryMapper.INSTANCE.toResponseEvent(txn),
                    outboxId
            );
            transactionManager.commit(tx);
        } catch (Exception e) {
            log.error("Error while persist transaction history: {}", e.getMessage());
            throw e;
        } finally {
            if (Objects.nonNull(tx) && !tx.isCompleted()) {
                transactionManager.rollback(tx);
            }
        }
        // send outbox event
        try (var p1 = new vn.vinclub.common.model.Profiler(getClass(), "processTopUpPointFailed - sendOutboxEvent")) {
            eventService.sendOutboxEvent(outboxId, null);
        } catch (Exception e) {
            log.error("Error while sending outbox event: {}", outboxId, e);
        }
    }

    @Profiler
    private void processTopUpPointSuccess(PartnerPointTxnResponse response) {
        var transactionId = response.getTransactionId();
        var partnerTransactionId = response.getPartnerTransactionId();

        // Init transaction
        var outboxId = UUID.randomUUID().toString();
        TransactionStatus tx = null;
        try (var p1 = new vn.vinclub.common.model.Profiler(getClass(), "processTopUpPointSuccess - transaction")) {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            tx = transactionManager.getTransaction(def);
            var txn = partnerPointTransactionHistoryService.makeTxnToPartnerSuccess(transactionId, partnerTransactionId, response.getRequest(), response.getResponse(), response.getProcessedTime());
            eventService.persistOutboxEvent(
                    PartnerPointTransactionHistoryMapper.INSTANCE.toResponseEvent(txn),
                    outboxId
            );
            transactionManager.commit(tx);
        } catch (Exception e) {
            log.error("Error while persist transaction history: {}", e.getMessage());
            throw e;
        } finally {
            if (Objects.nonNull(tx) && !tx.isCompleted()) {
                transactionManager.rollback(tx);
            }
        }
        // send outbox event
        try (var p1 = new vn.vinclub.common.model.Profiler(getClass(), "processTopUpPointSuccess - sendOutboxEvent")) {
            eventService.sendOutboxEvent(outboxId, null);
        } catch (Exception e) {
            log.error("Error while sending outbox event: {}", outboxId, e);
        }
    }

    private Optional<Long> checkDeleteCustomerFromHistorical(HistoricalEvent<CustomerHistoricalEventData> event) {
        if (event == null || event.getData() == null || event.getOldData() == null) {
            return Optional.empty();
        }

        CustomerHistoricalEventData data = event.getData();
        CustomerHistoricalEventData oldData = event.getOldData();

        if (BooleanUtils.isTrue(oldData.getActive().orElse(false)) && BooleanUtils.isFalse(data.getActive().orElse(false))) {
            return Optional.of(data.getCustomerId());
        }

        return Optional.empty();
    }
}
