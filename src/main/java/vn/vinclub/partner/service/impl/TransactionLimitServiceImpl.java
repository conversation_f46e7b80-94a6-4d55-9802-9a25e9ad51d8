package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.repository.PartnerPointTransactionHistoryRepository;
import vn.vinclub.partner.service.PartnerPointConfigService;
import vn.vinclub.partner.service.TransactionLimitService;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Set;

@Service
@Slf4j
@RequiredArgsConstructor
public class TransactionLimitServiceImpl implements TransactionLimitService {

    private static final String TRANSACTION_COUNT_CACHE_KEY = "partner_svc_daily_txn_count:%s:%s:%s";
    private static final String CASH_AMOUNT_CACHE_KEY = "partner_svc_daily_cash_amount:%s:%s:%s";
    private static final int CACHE_TTL_HOURS = 25; // Cache until next day
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final Set<PointHistoryStatus> COUNTED_STATUSES = Set.of(PointHistoryStatus.PROCESSING, PointHistoryStatus.SUCCESS);

    private final PartnerPointTransactionHistoryRepository repository;
    private final PartnerPointConfigService partnerPointConfigService;
    private final RedissonClient redissonClient;

    @Value("${partner.transaction.max-request-by-user-per-day}")
    private Long maxRequestByUserPerDay;

    @Value("${partner.transaction.max-cash-amount-by-user-per-day}")
    private Long maxCashAmountByUserPerDay;

    @Override
    public void validateDailyLimits(Long partnerId, String partnerUserId, Long pointAmount, String pointCode) {
        try (var p = new Profiler(getClass(), "validateDailyLimits")) {
        String today = LocalDate.now().format(DATE_FORMATTER);
        
        // Check transaction count limit
        Long currentCount = getDailyTransactionCount(partnerId, partnerUserId, today);
        if (currentCount >= maxRequestByUserPerDay) {
            log.warn("Daily transaction count limit exceeded. partnerId={}, partnerUserId={}, currentCount={}, limit={}", 
                    partnerId, partnerUserId, currentCount, maxRequestByUserPerDay);
            throw new BusinessLogicException(AppErrorCode.DAILY_TRANSACTION_COUNT_LIMIT_EXCEEDED);
        }

        // Check cash amount limit
        BigDecimal cashValue = calculateCashValue(pointAmount, pointCode, partnerId);
        BigDecimal currentCashAmount = getDailyCashAmount(partnerId, partnerUserId, today);
        BigDecimal newTotalCashAmount = currentCashAmount.add(cashValue);
        
        if (newTotalCashAmount.compareTo(BigDecimal.valueOf(maxCashAmountByUserPerDay)) > 0) {
            log.warn("Daily cash amount limit exceeded. partnerId={}, partnerUserId={}, currentAmount={}, newAmount={}, limit={}", 
                    partnerId, partnerUserId, currentCashAmount, newTotalCashAmount, maxCashAmountByUserPerDay);
            throw new BusinessLogicException(AppErrorCode.DAILY_CASH_AMOUNT_LIMIT_EXCEEDED);
        }
        }
    }

    @Override
    public Long getDailyTransactionCount(Long partnerId, String partnerUserId, String date) {
        try (var p = new Profiler(getClass(), "getDailyTransactionCount")) {
        String cacheKey = String.format(TRANSACTION_COUNT_CACHE_KEY, partnerId, partnerUserId, date);
        RBucket<Long> bucket = redissonClient.getBucket(cacheKey, new JsonJacksonCodec());
        
        if (bucket.isExists()) {
            return bucket.get();
        }

        // Calculate from database
        Long[] timeRange = getTimeRangeForDate(date);
        Long count = repository.countDailyTransactions(partnerId, partnerUserId, timeRange[0], timeRange[1]);
        
        // Cache the result
        bucket.set(count != null ? count : 0L, Duration.ofHours(CACHE_TTL_HOURS));

        return count != null ? count : 0L;
        }
    }

    @Override
    public BigDecimal getDailyCashAmount(Long partnerId, String partnerUserId, String date) {
        try (var p = new Profiler(getClass(), "getDailyCashAmount")) {
        String cacheKey = String.format(CASH_AMOUNT_CACHE_KEY, partnerId, partnerUserId, date);
        RBucket<BigDecimal> bucket = redissonClient.getBucket(cacheKey, new JsonJacksonCodec());
        
        if (bucket.isExists()) {
            return bucket.get();
        }

        // Calculate from database
        Long[] timeRange = getTimeRangeForDate(date);
        BigDecimal amount = repository.calculateDailyCashAmount(partnerId, partnerUserId, timeRange[0], timeRange[1]);
        
        // Cache the result
        bucket.set(amount != null ? amount : BigDecimal.ZERO, Duration.ofHours(CACHE_TTL_HOURS));

        return amount != null ? amount : BigDecimal.ZERO;
        }
    }

    @Override
    public void updateDailyCounters(PartnerPointTransactionHistory oldTransaction, PartnerPointTransactionHistory newTransaction) {
        try (var p = new Profiler(getClass(), "updateDailyCounters")) {
        // Handle transaction creation
        if (oldTransaction == null && newTransaction != null) {
            if (COUNTED_STATUSES.contains(newTransaction.getStatus())) {
                incrementCounters(newTransaction);
            }
            return;
        }

        // Handle transaction deletion
        if (oldTransaction != null && newTransaction == null) {
            if (COUNTED_STATUSES.contains(oldTransaction.getStatus())) {
                decrementCounters(oldTransaction);
            }
            return;
        }

        // Handle transaction update
        if (oldTransaction != null && newTransaction != null) {
            boolean oldCounted = COUNTED_STATUSES.contains(oldTransaction.getStatus());
            boolean newCounted = COUNTED_STATUSES.contains(newTransaction.getStatus());
            
            if (!oldCounted && newCounted) {
                // Transaction moved to counted status
                incrementCounters(newTransaction);
            } else if (oldCounted && !newCounted) {
                // Transaction moved from counted status
                decrementCounters(oldTransaction);
            }
        }
        }
    }

    @Override
    public void invalidateCache(Long partnerId, String partnerUserId, String date) {
        try (var p = new Profiler(getClass(), "invalidateCache")) {
        String countKey = String.format(TRANSACTION_COUNT_CACHE_KEY, partnerId, partnerUserId, date);
        String amountKey = String.format(CASH_AMOUNT_CACHE_KEY, partnerId, partnerUserId, date);

        redissonClient.getBucket(countKey).delete();
        redissonClient.getBucket(amountKey).delete();
        }
    }

    @Override
    public BigDecimal calculateCashValue(Long pointAmount, String pointCode, Long partnerId) {
        try (var p = new Profiler(getClass(), "calculateCashValue")) {
        try {
            var pointConfig = partnerPointConfigService.findByCode(partnerId, pointCode);
            if (pointConfig.getExchangeCashRate() != null) {
                BigDecimal exchangeValue = pointConfig.getExchangeCashRate().getExchangeValue();
                Long pointValue = pointConfig.getExchangeCashRate().getPointValue();
                
                if (exchangeValue != null && pointValue != null && pointValue > 0) {
                    BigDecimal pointCashValue = exchangeValue.divide(BigDecimal.valueOf(pointValue));
                    return pointCashValue.multiply(BigDecimal.valueOf(pointAmount));
                }
            }
        } catch (Exception e) {
            log.warn("Failed to calculate cash value for pointCode={}, partnerId={}, using 0", pointCode, partnerId, e);
        }

        return BigDecimal.ZERO;
        }
    }

    private void incrementCounters(PartnerPointTransactionHistory transaction) {
        try (var p = new Profiler(getClass(), "incrementCounters")) {
        String date = getDateFromTimestamp(transaction.getRequestTime());
        
        // Increment transaction count
        String countKey = String.format(TRANSACTION_COUNT_CACHE_KEY, 
                transaction.getPartnerId(), transaction.getPartnerUserId(), date);
        RBucket<Long> countBucket = redissonClient.getBucket(countKey, new JsonJacksonCodec());
        if (countBucket.isExists()) {
            countBucket.set(countBucket.get() + 1, Duration.ofHours(CACHE_TTL_HOURS));
        }
        
        // Increment cash amount
        BigDecimal cashValue = calculateCashValue(transaction.getPointAmount(), transaction.getPointCode(), transaction.getPartnerId());
        String amountKey = String.format(CASH_AMOUNT_CACHE_KEY, 
                transaction.getPartnerId(), transaction.getPartnerUserId(), date);
        RBucket<BigDecimal> amountBucket = redissonClient.getBucket(amountKey, new JsonJacksonCodec());
        if (amountBucket.isExists()) {
            amountBucket.set(amountBucket.get().add(cashValue), Duration.ofHours(CACHE_TTL_HOURS));
        }
        }
    }

    private void decrementCounters(PartnerPointTransactionHistory transaction) {
        try (var p = new Profiler(getClass(), "decrementCounters")) {
        String date = getDateFromTimestamp(transaction.getRequestTime());
        
        // Decrement transaction count
        String countKey = String.format(TRANSACTION_COUNT_CACHE_KEY, 
                transaction.getPartnerId(), transaction.getPartnerUserId(), date);
        RBucket<Long> countBucket = redissonClient.getBucket(countKey, new JsonJacksonCodec());
        if (countBucket.isExists()) {
            countBucket.set(Math.max(0L, countBucket.get() - 1), Duration.ofHours(CACHE_TTL_HOURS));
        }
        
        // Decrement cash amount
        BigDecimal cashValue = calculateCashValue(transaction.getPointAmount(), transaction.getPointCode(), transaction.getPartnerId());
        String amountKey = String.format(CASH_AMOUNT_CACHE_KEY, 
                transaction.getPartnerId(), transaction.getPartnerUserId(), date);
        RBucket<BigDecimal> amountBucket = redissonClient.getBucket(amountKey, new JsonJacksonCodec());
        if (amountBucket.isExists()) {
            BigDecimal newAmount = amountBucket.get().subtract(cashValue);
            amountBucket.set(newAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : newAmount, 
                    Duration.ofHours(CACHE_TTL_HOURS));
        }
        }
    }

    private Long[] getTimeRangeForDate(String date) {
        try (var p = new Profiler(getClass(), "getTimeRangeForDate")) {
        LocalDate localDate = LocalDate.parse(date, DATE_FORMATTER);
        long startTime = localDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        long endTime = localDate.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        return new Long[]{startTime, endTime};
        }
    }

    private String getDateFromTimestamp(Long timestamp) {
        try (var p = new Profiler(getClass(), "getDateFromTimestamp")) {
        return LocalDate.ofInstant(java.time.Instant.ofEpochMilli(timestamp), ZoneId.systemDefault())
                .format(DATE_FORMATTER);
        }
    }
}
