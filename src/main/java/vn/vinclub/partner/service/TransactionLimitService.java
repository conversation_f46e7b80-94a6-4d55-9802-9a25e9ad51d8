package vn.vinclub.partner.service;

import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;

import java.math.BigDecimal;

/**
 * Service for managing transaction limits and caching daily counters
 */
public interface TransactionLimitService {

    /**
     * Validate if a new transaction would exceed daily limits
     * @param partnerId Partner ID
     * @param partnerUserId Partner user ID
     * @param pointAmount Point amount for the transaction
     * @param pointCode Point code to calculate cash value
     * @throws vn.vinclub.partner.exception.BusinessLogicException if limits are exceeded
     */
    void validateDailyLimits(Long partnerId, String partnerUserId, Long pointAmount, String pointCode);

    /**
     * Get current daily transaction count for a user
     * @param partnerId Partner ID
     * @param partnerUserId Partner user ID
     * @param date Date in YYYY-MM-DD format
     * @return Current transaction count
     */
    Long getDailyTransactionCount(Long partnerId, String partnerUserId, String date);

    /**
     * Get current daily cash amount for a user
     * @param partnerId Partner ID
     * @param partnerUserId Partner user ID
     * @param date Date in YYYY-MM-DD format
     * @return Current cash amount
     */
    BigDecimal getDailyCashAmount(Long partnerId, String partnerUserId, String date);

    /**
     * Update daily counters when transaction status changes
     * @param oldTransaction Old transaction state (can be null for new transactions)
     * @param newTransaction New transaction state (can be null for deleted transactions)
     */
    void updateDailyCounters(PartnerPointTransactionHistory oldTransaction, PartnerPointTransactionHistory newTransaction);

    /**
     * Invalidate cache for a specific user and date
     * @param partnerId Partner ID
     * @param partnerUserId Partner user ID
     * @param date Date in YYYY-MM-DD format
     */
    void invalidateCache(Long partnerId, String partnerUserId, String date);

    /**
     * Calculate cash value for a point amount and point code
     * @param pointAmount Point amount
     * @param pointCode Point code
     * @param partnerId Partner ID
     * @return Cash value in VND
     */
    BigDecimal calculateCashValue(Long pointAmount, String pointCode, Long partnerId);
}
